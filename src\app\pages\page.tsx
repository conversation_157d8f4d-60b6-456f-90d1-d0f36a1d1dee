import Hero from "@/components/sections/Hero";
import MortgageCalculator from "@/components/sections/MortgageCalculator/MortgageCalculator";
// Import other sections as you build them
// import { getDictionary } from "@/lib/i18n/dictionaries";

export default async function HomePage() {
  // const lang = 'en'; // Placeholder
  // const dict = await getDictionary(lang);
  
  return (
    <>
      <Hero />
      <MortgageCalculator />
      {/* <WhyChooseUs /> */}
      {/* <Rates /> */}
      {/* <Team /> */}
      {/* <Testimonials /> */}
      {/* <CallToAction /> */}
      {/* <ApplicationForm /> */}
    </>
  );
}