export default function Rates() {
  const rates = [
    {
      id: '3-year',
      title: '3-Year Fixed',
      rate: '3.69',
      term: '3-Year Term',
      badge: { text: 'Best Value', type: 'best' },
      features: [
        'Rate guarantee for 120 days',
        'Portable mortgage option',
        '20% annual prepayment',
        'No prepayment penalties'
      ],
      savings: 'Save $156/month vs. big banks',
      featured: true
    },
    {
      id: '5-year',
      title: '5-Year Fixed',
      rate: '3.94',
      term: '5-Year Term',
      badge: { text: 'Most Popular', type: 'popular' },
      features: [
        'Long-term rate stability',
        'Budget certainty',
        'Renewal options',
        'Payment flexibility'
      ],
      savings: 'Save $134/month vs. big banks',
      featured: false
    },
    {
      id: 'variable',
      title: 'Variable Rate',
      rate: '3.45',
      term: 'Prime - 0.75%',
      badge: { text: 'Trending', type: 'trending' },
      features: [
        'Lowest starting rate',
        'Rate flexibility',
        'Convert to fixed anytime',
        'Payment stability options'
      ],
      savings: 'Save $189/month vs. big banks',
      featured: false
    }
  ];

  const getBadgeStyles = (type: string) => {
    switch (type) {
      case 'best':
        return 'bg-accent-gold text-neutral-gray-dark';
      case 'popular':
        return 'bg-secondary-orange text-white';
      case 'trending':
        return 'bg-primary-blue text-white';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  return (
    <section id="rates" className="py-20 bg-light-background">
      <div className="container mx-auto px-6">
        <div className="section-header mb-16">
          <h2>Today's Best Rates</h2>
          <p>AI-matched rates updated in real-time from our network of 50+ lenders</p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {rates.map((rate) => (
            <div 
              key={rate.id}
              className={`bg-white rounded-xl shadow-card hover:shadow-card-hover transition-all duration-300 transform hover:-translate-y-2 ${
                rate.featured ? 'ring-2 ring-accent-gold scale-105' : ''
              }`}
            >
              {/* Rate Header */}
              <div className="p-6 border-b border-gray-100">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-primary-blue-dark">
                    {rate.title}
                  </h3>
                  <span className={`px-3 py-1 rounded-full text-xs font-bold ${getBadgeStyles(rate.badge.type)}`}>
                    {rate.badge.text}
                  </span>
                </div>
                
                <div className="text-center">
                  <div className="text-4xl font-bold text-primary-blue mb-2">
                    {rate.rate}%
                  </div>
                  <div className="text-sm text-neutral-gray font-medium">
                    {rate.term}
                  </div>
                </div>
              </div>

              {/* Rate Features */}
              <div className="p-6">
                <div className="space-y-3 mb-6">
                  {rate.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-success-green text-sm">✓</span>
                      <span className="text-sm text-neutral-gray-dark">{feature}</span>
                    </div>
                  ))}
                </div>
                
                {/* Savings Display */}
                <div className="bg-success-green/10 border border-success-green/20 rounded-lg p-3 mb-6">
                  <div className="text-center">
                    <span className="text-sm font-semibold text-success-green">
                      {rate.savings}
                    </span>
                  </div>
                </div>
                
                {/* Get Rate Button */}
                <button className="w-full bg-primary-blue text-white font-bold py-3 px-6 rounded-lg hover:bg-primary-blue-light transition-colors">
                  Get This Rate
                </button>
              </div>
            </div>
          ))}
        </div>
        
        {/* Additional Info */}
        <div className="text-center mt-12">
          <p className="text-neutral-gray text-sm mb-4">
            Rates shown are for qualified borrowers and subject to change. 
            <br />
            Contact us for personalized rate quotes based on your specific situation.
          </p>
          <div className="flex items-center justify-center space-x-6 text-sm">
            <div className="flex items-center space-x-2">
              <span className="text-lg">🤖</span>
              <span className="font-semibold text-neutral-gray-dark">AI-Matched Rates</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-lg">🔄</span>
              <span className="font-semibold text-neutral-gray-dark">Updated Daily</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-lg">🛡️</span>
              <span className="font-semibold text-neutral-gray-dark">Rate Protected</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
