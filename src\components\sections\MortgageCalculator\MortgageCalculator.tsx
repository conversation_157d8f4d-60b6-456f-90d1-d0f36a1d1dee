"use client";
import { useState, useMemo } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function MortgageCalculator() {
  const { dict } = useLanguage();
  const [homePrice, setHomePrice] = useState(650000);
  const [downPayment, setDownPayment] = useState(130000);
  const [interestRate, setInterestRate] = useState(3.69);
  const [amortization, setAmortization] = useState(25);

  // Format number with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-CA').format(num);
  };

  // Format currency
  const formatCurrency = (num: number) => {
    return new Intl.NumberFormat('en-CA', { style: 'currency', currency: 'CAD' }).format(num);
  };

  // Calculate down payment percentage
  const downPaymentPercentage = useMemo(() => {
    return homePrice > 0 ? ((downPayment / homePrice) * 100).toFixed(1) : '0.0';
  }, [homePrice, downPayment]);

  // Calculate all mortgage values
  const calculations = useMemo(() => {
    const loanAmount = homePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;
    const numberOfPayments = amortization * 12;

    if (loanAmount <= 0 || monthlyRate <= 0) {
      return {
        monthlyPayment: 0,
        loanAmount,
        totalInterest: 0,
        totalPaid: loanAmount,
        propertyTax: 0,
        insurance: 0,
        totalMonthly: 0
      };
    }

    const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    const totalPaid = monthlyPayment * numberOfPayments;
    const totalInterest = totalPaid - loanAmount;

    // Estimate property tax (1% annually) and insurance (0.3% annually)
    const propertyTax = (homePrice * 0.01) / 12;
    const insurance = (homePrice * 0.003) / 12;
    const totalMonthly = monthlyPayment + propertyTax + insurance;

    return {
      monthlyPayment,
      loanAmount,
      totalInterest,
      totalPaid,
      propertyTax,
      insurance,
      totalMonthly
    };
  }, [homePrice, downPayment, interestRate, amortization]);

  // Handle input changes
  const handleHomePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value.replace(/,/g, '')) || 0;
    setHomePrice(value);
  };

  const handleDownPaymentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value.replace(/,/g, '')) || 0;
    setDownPayment(value);
  };

  return (
    <section id="calculator" className="py-20 bg-light-background">
      <div className="container mx-auto px-6">
        <div className="section-header mb-12">
          <h2>{dict.Calculator.title}</h2>
          <p>{dict.Calculator.subtitle}</p>
        </div>

        <div className="bg-white p-8 md:p-12 rounded-2xl shadow-card max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Calculator Inputs */}
            <div className="space-y-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-2xl font-bold text-primary-blue-dark">{dict.Calculator.calculateYourPayment}</h3>
                <div className="flex items-center space-x-2 bg-accent-gold-light px-3 py-1 rounded-full">
                  <span className="text-lg">🛡️</span>
                  <span className="text-sm font-semibold text-neutral-gray-dark">{dict.Calculator.rateGuaranteed}</span>
                </div>
              </div>

              {/* Home Price and Down Payment Row */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="homePrice" className="block text-sm font-semibold text-neutral-gray-dark">
                    Home Price
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-gray font-semibold">$</span>
                    <input
                      type="text"
                      id="homePrice"
                      value={formatNumber(homePrice)}
                      onChange={handleHomePriceChange}
                      className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent text-lg font-semibold"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="downPayment" className="block text-sm font-semibold text-neutral-gray-dark">
                    Down Payment
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-gray font-semibold">$</span>
                    <input
                      type="text"
                      id="downPayment"
                      value={formatNumber(downPayment)}
                      onChange={handleDownPaymentChange}
                      className="w-full pl-8 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent text-lg font-semibold"
                    />
                    <div className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-primary-blue text-white px-2 py-1 rounded text-sm font-bold">
                      {downPaymentPercentage}%
                    </div>
                  </div>
                </div>
              </div>

              {/* Interest Rate and Amortization Row */}
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <label htmlFor="interestRate" className="block text-sm font-semibold text-neutral-gray-dark">
                    Interest Rate
                  </label>
                  <div className="relative">
                    <input
                      type="number"
                      id="interestRate"
                      value={interestRate}
                      onChange={(e) => setInterestRate(parseFloat(e.target.value) || 0)}
                      step="0.01"
                      min="0"
                      max="20"
                      className="w-full pl-4 pr-8 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-blue focus:border-transparent text-lg font-semibold"
                    />
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-neutral-gray font-semibold">%</span>
                  </div>
                </div>

                <div className="space-y-2">
                  <label htmlFor="amortization" className="block text-sm font-semibold text-neutral-gray-dark">
                    Amortization
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      id="amortization"
                      min="1"
                      max="30"
                      value={amortization}
                      onChange={(e) => setAmortization(parseInt(e.target.value))}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="text-center">
                      <span className="text-lg font-bold text-primary-blue">{amortization} years</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Calculate Button */}
              <button className="w-full bg-primary-blue text-white font-bold py-4 px-6 rounded-lg hover:bg-primary-blue-light transition-colors flex items-center justify-center space-x-2">
                <span className="text-xl">🧮</span>
                <span>Calculate Payment</span>
              </button>
            </div>

            {/* Calculator Results */}
            <div className="space-y-6">
              {/* Monthly Payment Card */}
              <div className="bg-gradient-to-br from-primary-blue to-primary-blue-light text-white p-8 rounded-xl">
                <h3 className="text-lg font-semibold opacity-90 mb-2">Monthly Payment</h3>
                <div className="text-4xl font-bold text-accent-gold-light mb-6">
                  {formatCurrency(calculations.monthlyPayment)}
                </div>

                <div className="space-y-3 bg-white/10 p-4 rounded-lg">
                  <div className="flex justify-between text-sm">
                    <span>Principal & Interest</span>
                    <span className="font-semibold">{formatCurrency(calculations.monthlyPayment)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Property Tax (est.)</span>
                    <span className="font-semibold">{formatCurrency(calculations.propertyTax)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Insurance (est.)</span>
                    <span className="font-semibold">{formatCurrency(calculations.insurance)}</span>
                  </div>
                  <div className="border-t border-white/20 pt-2 mt-2">
                    <div className="flex justify-between font-bold">
                      <span>Total Monthly</span>
                      <span>{formatCurrency(calculations.totalMonthly)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Loan Summary */}
              <div className="bg-gray-50 p-6 rounded-xl">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-bold text-neutral-gray-dark">Loan Summary</h4>
                  <div className="flex items-center space-x-2 bg-accent-gold-light px-3 py-1 rounded-full">
                    <span className="text-sm">🤖</span>
                    <span className="text-xs font-bold text-neutral-gray-dark">AI-Optimized</span>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-neutral-gray">Loan Amount</span>
                    <span className="font-bold text-neutral-gray-dark">{formatCurrency(calculations.loanAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-gray">Total Interest</span>
                    <span className="font-bold text-neutral-gray-dark">{formatCurrency(calculations.totalInterest)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-neutral-gray">Total Paid</span>
                    <span className="font-bold text-neutral-gray-dark">{formatCurrency(calculations.totalPaid)}</span>
                  </div>
                </div>

                <div className="mt-4 p-3 bg-success-green/10 rounded-lg border border-success-green/20">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">💰</span>
                    <span className="text-sm font-semibold text-success-green">
                      You could save $18,400 vs. national average
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}