"use client";
import { useState, useMemo } from 'react';
import CalculatorResults from './CalculatorResults';

export default function MortgageCalculator() {
  const [homePrice, setHomePrice] = useState(650000);
  const [downPayment, setDownPayment] = useState(130000);
  const [interestRate, setInterestRate] = useState(3.69);
  const [amortization, setAmortization] = useState(25);

  const results = useMemo(() => {
    const loanAmount = homePrice - downPayment;
    const monthlyRate = interestRate / 100 / 12;
    const numberOfPayments = amortization * 12;

    if (loanAmount <= 0 || monthlyRate <= 0) {
      return { monthlyPayment: 0, loanAmount, totalInterest: 0, totalPaid: loanAmount };
    }
    
    const monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, numberOfPayments)) / (Math.pow(1 + monthlyRate, numberOfPayments) - 1);
    const totalPaid = monthlyPayment * numberOfPayments;
    const totalInterest = totalPaid - loanAmount;

    return { monthlyPayment, loanAmount, totalInterest, totalPaid };
  }, [homePrice, downPayment, interestRate, amortization]);


  return (
    <section id="calculator" className="py-20 bg-light-background">
        <div className="container mx-auto px-6">
            <div className="section-header mb-12">
                <h2>Smart Mortgage Calculator</h2>
                <p>Get instant payment estimates with our AI-powered calculator.</p>
            </div>
            <div className="bg-white p-8 md:p-12 rounded-2xl shadow-card max-w-5xl mx-auto grid md:grid-cols-2 gap-12">
                {/* Inputs */}
                <div>
                   {/* Form fields here */}
                   <h3 className="text-primary-blue-dark font-bold text-2xl mb-6">Calculate Your Payment</h3>
                   {/* ... add input fields for homePrice, downPayment etc. */}
                </div>
                {/* Results */}
                <CalculatorResults data={results} />
            </div>
        </div>
    </section>
  );
}