import type { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Our Mortgage Services',
  description: 'Explore our range of mortgage services including home purchase, refinancing, renewals, and investment properties.',
};

const services = [
    { title: "Home Purchase", description: "Whether you're a first-time buyer or moving to a new home, we make the process simple.", icon: "🏠", href: "/purchase" },
    { title: "Refinancing", description: "Lower your monthly payment, access equity, or consolidate debt with a better rate.", icon: "💰", href: "/refinancing" },
    { title: "Mortgage Renewal", description: "Let us scan the market to ensure you get the absolute best rate, guaranteed.", icon: "🔄", href: "/renewal" },
    { title: "Investment Properties", description: "Expand your real estate portfolio with expert advice and financing.", icon: "📈", href: "/investment" },
]

export default function ServicesPage() {
  return (
    <section className="py-20 pt-32">
        <div className="container mx-auto px-6">
             <div className="section-header mb-12">
                <h2>Our Mortgage Services</h2>
                <p>Tailored mortgage solutions to meet your unique financial goals.</p>
            </div>
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                {services.map(service => (
                    <Link href={service.href} key={service.title} className="block bg-white p-8 rounded-xl shadow-card hover:shadow-card-hover hover:-translate-y-2 transition-all">
                        <div className="text-4xl mb-4">{service.icon}</div>
                        <h3 className="text-xl font-bold text-primary-blue-dark mb-2">{service.title}</h3>
                        <p className="text-neutral-gray text-sm">{service.description}</p>
                    </Link>
                ))}
            </div>
        </div>
    </section>
  );
}