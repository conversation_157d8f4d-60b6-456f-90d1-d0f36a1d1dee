"use client";
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { LanguageToggle } from '@/components/LanguageToggle'; // We'll create this next

// Pass the dictionary as a prop
export default function Header({ dict }: { dict: any }) {
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { href: '#rates', label: dict.rates },
    { href: '#calculator', label: dict.calculator },
    { href: '/services', label: dict.services },
    { href: '#about', label: dict.about },
    { href: '#team', label: dict.team },
    { href: '#contact', label: dict.contact },
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-shadow duration-300 ${isScrolled ? 'bg-white shadow-md' : 'bg-transparent'}`}>
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          <Link href="/" className="text-2xl font-bold text-primary-blue">
            TrueRate Digital
            <span className="block text-sm font-medium text-secondary-orange">{dict.tagline}</span>
          </Link>
          <nav className="hidden md:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link key={link.href} href={link.href} className="text-neutral-gray font-medium hover:text-primary-blue transition-colors">
                {link.label}
              </Link>
            ))}
          </nav>
          <div className="flex items-center space-x-4">
            {/* <LanguageToggle /> */}
            <span className="hidden lg:inline font-semibold text-lg text-neutral-gray">1-877-TRUERATE</span>
            <Link href="#apply" className="bg-secondary-orange text-white font-bold py-2 px-5 rounded-lg shadow-md hover:bg-secondary-orange-light transition-all transform hover:-translate-y-0.5">
              {dict.getStarted}
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
}