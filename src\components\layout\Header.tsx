"use client";
import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false);
  const { language, dict, toggleLanguage } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navLinks = [
    { href: '#rates', label: dict.Header.rates },
    { href: '#calculator', label: dict.Header.calculator },
    { href: '/services', label: dict.Header.services },
    { href: '#about', label: dict.Header.about },
    { href: '#team', label: dict.Header.team },
    { href: '#contact', label: dict.Header.contact },
  ];

  return (
    <header className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md' : 'bg-transparent'}`}>
      <div className="container mx-auto px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Logo */}
          <Link href="/" className="flex flex-col">
            <h1 className="text-2xl font-bold text-primary-blue">TrueRate Digital</h1>
            <span className="text-sm font-medium text-secondary-orange">{dict.Header.tagline}</span>
          </Link>

          {/* Navigation */}
          <nav className="hidden lg:flex items-center space-x-8">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="text-neutral-gray font-medium hover:text-primary-blue transition-colors"
              >
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Actions */}
          <div className="flex items-center space-x-4">
            {/* Language Toggle */}
            <button
              onClick={toggleLanguage}
              className="flex items-center justify-center w-10 h-10 bg-white border border-gray-200 rounded-lg hover:border-primary-blue transition-colors"
            >
              <span className="text-sm font-semibold text-primary-blue">
                {language === 'en' ? 'FR' : 'EN'}
              </span>
            </button>

            {/* Phone Number */}
            <span className="hidden md:inline font-semibold text-neutral-gray">
              1-877-TRUERATE
            </span>

            {/* Get Started Button */}
            <Link
              href="#apply"
              className="bg-secondary-orange text-white font-bold py-2 px-6 rounded-lg shadow-md hover:bg-secondary-orange-light transition-all transform hover:-translate-y-0.5"
            >
              {dict.Header.getStarted}
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
}