import Link from 'next/link';

export default function Footer() {
  return (
    <footer id="contact" className="bg-gray-900 text-white">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Footer content goes here, converted to Tailwind CSS */}
          <div>
            <h3 className="text-xl font-bold mb-4">TrueRate Digital</h3>
            <p className="text-neutral-gray-light text-sm">Canada's most trusted digital mortgage platform.</p>
          </div>
          <div>
            <h4 className="font-bold mb-4">Services</h4>
            <ul className="space-y-2 text-sm">
              <li><Link href="/purchase" className="text-neutral-gray-light hover:text-secondary-orange-light">Home Purchase</Link></li>
              <li><Link href="/refinancing" className="text-neutral-gray-light hover:text-secondary-orange-light">Refinancing</Link></li>
              <li><Link href="/renewal" className="text-neutral-gray-light hover:text-secondary-orange-light">Mortgage Renewal</Link></li>
            </ul>
          </div>
          <div>
            <h4 className="font-bold mb-4">Tools & Resources</h4>
            <ul className="space-y-2 text-sm">
               <li><Link href="#calculator" className="text-neutral-gray-light hover:text-secondary-orange-light">Mortgage Calculator</Link></li>
               <li><Link href="#rates" className="text-neutral-gray-light hover:text-secondary-orange-light">Rate Comparison</Link></li>
            </ul>
          </div>
          <div>
             <h4 className="font-bold mb-4">Contact</h4>
             <p className="text-sm text-neutral-gray-light">📞 1-877-TRUERATE</p>
             <p className="text-sm text-neutral-gray-light">✉️ <EMAIL></p>
          </div>
        </div>
        <div className="mt-12 border-t border-neutral-gray-dark pt-6 text-center text-sm text-neutral-gray">
          <p>&copy; 2024 TrueRate Digital. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}