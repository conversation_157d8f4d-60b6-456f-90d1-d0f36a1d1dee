export default function WhyChooseUs() {
  const features = [
    {
      id: 'trust',
      icon: '🏆',
      title: '5-Star Trusted Service',
      description: 'Over 15,000 satisfied customers with an average 5.0-star rating. Our proven track record speaks for itself.',
      stats: [
        { icon: '⭐', text: '5.0/5 Rating' },
        { icon: '📈', text: '15,000+ Customers' }
      ],
      className: 'trust'
    },
    {
      id: 'innovation',
      icon: '🤖',
      title: 'AI-Powered Matching',
      description: 'Our advanced AI analyzes your profile and matches you with the perfect lender and rate in real-time.',
      stats: [
        { icon: '⚡', text: '2-Min Process' },
        { icon: '🎯', text: 'Perfect Matches' }
      ],
      className: 'innovation'
    },
    {
      id: 'guarantee',
      icon: '🛡️',
      title: 'Rate Guarantee Promise',
      description: 'We guarantee your rate for 120 days and will match any competitor\'s rate or beat it by 0.1%.',
      stats: [
        { icon: '🔒', text: '120-Day Lock' },
        { icon: '💯', text: 'Best Rate Promise' }
      ],
      className: 'guarantee'
    },
    {
      id: 'speed',
      icon: '🚀',
      title: 'Lightning Fast Process',
      description: 'Get pre-approved in 2 minutes and close in as little as 18 days with our streamlined digital process.',
      stats: [
        { icon: '⏱️', text: '2-Min Pre-Approval' },
        { icon: '📋', text: '18-Day Close' }
      ],
      className: 'speed'
    }
  ];

  return (
    <section id="about" className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="section-header mb-16">
          <h2>Why Choose TrueRate Digital?</h2>
          <p>The perfect combination of trusted expertise and innovative technology</p>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature) => (
            <div 
              key={feature.id} 
              className="bg-white p-8 rounded-xl shadow-card hover:shadow-card-hover transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
            >
              <div className="text-center mb-6">
                <div className="text-5xl mb-4">{feature.icon}</div>
                <h3 className="text-xl font-bold text-primary-blue-dark mb-3">
                  {feature.title}
                </h3>
                <p className="text-neutral-gray text-sm leading-relaxed">
                  {feature.description}
                </p>
              </div>
              
              <div className="space-y-3">
                {feature.stats.map((stat, index) => (
                  <div 
                    key={index} 
                    className="flex items-center justify-center space-x-2 bg-light-background px-3 py-2 rounded-lg"
                  >
                    <span className="text-lg">{stat.icon}</span>
                    <span className="text-sm font-semibold text-neutral-gray-dark">
                      {stat.text}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
