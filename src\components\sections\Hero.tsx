"use client"; // This directive makes the component interactive

import { useState } from 'react'; // Import useState for managing state
import Image from 'next/image';
import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';

export default function Hero() {
  // State to track which goal button is currently active. 'buy' is the default.
  const [activeGoal, setActiveGoal] = useState('buy');
  const { dict } = useLanguage();

  // Data for the goal buttons to keep our JSX clean
  const goals = [
    { id: 'buy', icon: '🏠', title: dict.Hero.buyHome, desc: dict.Hero.buyDesc },
    { id: 'renew', icon: '🔄', title: dict.Hero.renew, desc: dict.Hero.renewDesc },
    { id: 'refinance', icon: '💰', title: dict.Hero.refinance, desc: dict.Hero.refinanceDesc },
  ];

  // Data for the trust indicators
  const trustIndicators = [
    { icon: '⭐', text: dict.Hero.starRating },
    { icon: '🛡️', text: dict.<PERSON>.rateGuarantee },
    { icon: '🚀', text: dict.Hero.preApproval },
  ];

  return (
    <section className="bg-gradient-to-br from-light-background to-blue-100 pt-32 pb-20 min-h-screen flex items-center">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          
          {/* == LEFT COLUMN: TEXT CONTENT == */}
          <div className="text-center md:text-left flex flex-col gap-8">
            <div>
              <span className="inline-flex items-center bg-white rounded-full p-1 pr-4 mb-6 shadow-md border-2 border-accent-gold-light">
                <span className="bg-accent-gold-light text-yellow-800 rounded-full px-2 py-1 mr-3 text-lg">🏆</span>
                <span className="font-semibold text-primary-blue text-sm">{dict.Hero.badge}</span>
              </span>
              <h2 className="text-5xl font-extrabold text-primary-blue-dark leading-tight mb-4">
                {dict.Hero.title}
              </h2>
              <p className="text-lg text-neutral-gray">
                {dict.Hero.subtitle}
              </p>
            </div>

            {/* == NEW: GOAL SELECTOR SECTION == */}
            <div>
              <h3 className="text-lg font-semibold text-neutral-gray-dark mb-3">{dict.Hero.goalQuestion}</h3>
              <div className="grid grid-cols-3 gap-3">
                {goals.map((goal) => (
                  <button
                    key={goal.id}
                    onClick={() => setActiveGoal(goal.id)}
                    className={`p-4 rounded-xl border-2 transition-all transform hover:-translate-y-1 ${
                      activeGoal === goal.id
                        ? 'bg-primary-blue text-white border-primary-blue shadow-lg'
                        : 'bg-white text-neutral-gray-dark border-gray-200 hover:border-primary-blue-light'
                    }`}
                  >
                    <span className="text-3xl block mb-2">{goal.icon}</span>
                    <span className="font-bold block">{goal.title}</span>
                    <span className="text-xs opacity-80 block">{goal.desc}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* == CTA BUTTONS == */}
            <div className="flex justify-center md:justify-start space-x-4">
              <Link href="#apply" className="bg-secondary-orange text-white font-bold py-3 px-8 rounded-lg shadow-lg hover:bg-secondary-orange-light transition-all transform hover:-translate-y-1">{dict.Hero.applyNow}</Link>
              <Link href="#calculator" className="bg-white text-primary-blue font-bold py-3 px-8 rounded-lg shadow-lg border-2 border-primary-blue hover:bg-blue-50 transition-all transform hover:-translate-y-1">{dict.Hero.calculatePayment}</Link>
            </div>
            
            {/* == TRUST INDICATORS == */}
            <div className="flex justify-center md:justify-start items-center gap-6 pt-4">
              {trustIndicators.map((indicator) => (
                <div key={indicator.text} className="flex items-center gap-2 bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg shadow-sm border border-gray-100">
                  <span className="text-lg">{indicator.icon}</span>
                  <span className="font-semibold text-neutral-gray-dark text-sm">{indicator.text}</span>
                </div>
              ))}
            </div>
          </div>

          {/* == RIGHT COLUMN: IMAGE CONTENT == */}
          <div className="relative">
            <Image 
              src="/images/jLYYENGGEva9.jpg" 
              alt="Happy family with their new home" 
              width={600} 
              height={400} 
              className="rounded-2xl shadow-xl w-full h-auto object-cover"
              priority
            />
            <div className="absolute top-4 right-4 bg-white p-4 rounded-xl shadow-card text-center border-4 border-accent-gold">
              <p className="text-sm font-medium text-neutral-gray">Best Rate Today</p>
              <p className="text-4xl font-bold text-primary-blue my-1">3.69%</p>
              <p className="text-sm text-neutral-gray">3-Year Fixed</p>
              <div className="mt-2 bg-accent-gold-light text-neutral-gray-dark text-xs font-bold py-1 px-2 rounded-md">
                🤖 AI-Matched
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}