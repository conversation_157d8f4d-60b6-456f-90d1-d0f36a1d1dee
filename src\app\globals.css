/* 1. Import Tailwind's core styles using the modern v4 syntax */
@import "tailwindcss/preflight"; /* The CSS reset (like normalize.css) */
@import "tailwindcss/utilities"; /* All the utility classes (bg-blue-500, etc.) */


/* 2. Define your own custom global styles and component classes */
@layer base {
  body {
    @apply bg-white text-neutral-gray-dark antialiased;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply text-primary-blue font-bold;
  }
  h2 {
    @apply text-4xl;
  }
  h3 {
    @apply text-2xl;
  }
}

@layer components {
  .section-header {
    @apply text-center max-w-2xl mx-auto;
  }
  .section-header h2 {
    @apply mb-4;
  }
  .section-header p {
    @apply text-xl text-neutral-gray;
  }
}