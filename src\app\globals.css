/* 1. Import Tailwind's core styles using the modern v4 syntax */
@import "tailwindcss/preflight"; /* The CSS reset (like normalize.css) */
@import "tailwindcss/utilities"; /* All the utility classes (bg-blue-500, etc.) */


/* 2. Define your own custom global styles and component classes */
@layer base {
  body {
    @apply bg-white text-neutral-gray-dark antialiased;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply text-primary-blue font-bold;
  }
  h2 {
    @apply text-4xl;
  }
  h3 {
    @apply text-2xl;
  }
}

@layer components {
  .section-header {
    @apply text-center max-w-2xl mx-auto;
  }
  .section-header h2 {
    @apply mb-4;
  }
  .section-header p {
    @apply text-xl text-neutral-gray;
  }

  /* Custom slider styling */
  .slider {
    @apply appearance-none bg-gray-200 rounded-lg outline-none;
  }

  .slider::-webkit-slider-thumb {
    @apply appearance-none w-6 h-6 bg-primary-blue rounded-full cursor-pointer shadow-lg;
  }

  .slider::-moz-range-thumb {
    @apply w-6 h-6 bg-primary-blue rounded-full cursor-pointer border-0 shadow-lg;
  }

  /* Enhanced card hover effects */
  .card-hover-lift {
    @apply transition-all duration-300 transform hover:-translate-y-2 hover:shadow-card-hover;
  }

  /* Gradient text effect */
  .gradient-text {
    @apply bg-gradient-to-r from-primary-blue to-secondary-orange bg-clip-text text-transparent;
  }
}