import type { Config } from 'tailwindcss'

// This is the correct configuration for Tailwind CSS v4.
// It defines your custom theme but relies on the PostCSS plugin for content detection.
export default {
  theme: {
    extend: {
      colors: {
        'primary-blue': '#1e40af',
        'primary-blue-light': '#3b82f6',
        'primary-blue-dark': '#1e3a8a',
        'secondary-orange': '#ea580c',
        'secondary-orange-light': '#fb923c',
        'accent-gold': '#fbbf24',
        'accent-gold-light': '#fcd34d',
        'success-green': '#10b981',
        'error-red': '#ef4444',
        'neutral-gray': '#64748b',
        'neutral-gray-light': '#94a3b8',
        'neutral-gray-dark': '#475569',
        'light-background': '#f8fafc',
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
      },
      boxShadow: {
        'card': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
        'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'glow': '0 0 20px rgba(59, 130, 246, 0.3)',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'bounce-gentle': 'bounceGentle 2s infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        bounceGentle: {
          '0%, 100%': { transform: 'translateY(0)' },
          '50%': { transform: 'translateY(-5px)' },
        },
      },
    },
  },
} satisfies Config