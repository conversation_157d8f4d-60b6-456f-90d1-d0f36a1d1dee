import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import Header from "../components/layout/Header";
import Footer from "../components/layout/Footer";

// This import statement is what tells Next.js to load your CSS file.
import "./globals.css";

// This is a temporary measure for our dictionary until we build full i18n routing.
import dict from '@/messages/en.json';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    default: "TrueRate Digital - Canada's Most Trusted Digital Mortgage Experience",
    template: "%s - TrueRate Digital",
  },
  description: "Combining 5-star service with AI-powered innovation to find you the perfect rate in minutes.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="!scroll-smooth">
      <body className={inter.className}>
        <div className="flex flex-col min-h-screen">
          <Header dict={dict.Header} />
          <main className="flex-grow">
            {children}
          </main>
          <Footer />
        </div>
      </body>
    </html>
  );
}