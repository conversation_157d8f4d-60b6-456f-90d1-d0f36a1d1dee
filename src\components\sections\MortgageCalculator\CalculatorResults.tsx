// A simple component to display results.
// You can add animations here with framer-motion.
export default function CalculatorResults({ data }: { data: any }) {
const { monthlyPayment, loanAmount } = data;
const formatCurrency = (num: number) =>
new Intl.NumberFormat('en-CA', { style: 'currency', currency: 'CAD' }).format(num);
return (
<div className="bg-gradient-to-br from-primary-blue to-primary-blue-light text-white p-8 rounded-xl flex flex-col justify-center items-center text-center">
<h3 className="text-lg font-semibold opacity-80 mb-2">Your Estimated Monthly Payment</h3>
<p className="text-5xl font-bold text-accent-gold-light mb-6">
{formatCurrency(monthlyPayment)}
</p>
<div className="w-full bg-white/10 p-4 rounded-lg">
<div className="flex justify-between text-sm">
<span>Loan Amount:</span>
<span className="font-bold">{formatCurrency(loanAmount)}</span>
</div>
</div>
</div>
);
}